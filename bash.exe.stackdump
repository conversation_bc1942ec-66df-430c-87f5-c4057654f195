Stack trace:
Frame         Function      Args
0007FFFF8EA0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF8EA0, 0007FFFF7DA0) msys-2.0.dll+0x1FE8E
0007FFFF8EA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9178) msys-2.0.dll+0x67F9
0007FFFF8EA0  000210046832 (000210286019, 0007FFFF8D58, 0007FFFF8EA0, 000000000000) msys-2.0.dll+0x6832
0007FFFF8EA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8EA0  000210068E24 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9180  00021006A225 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE27330000 ntdll.dll
7FFE26B50000 KERNEL32.DLL
7FFE24D00000 KERNELBASE.dll
7FFE25350000 USER32.dll
7FFE25000000 win32u.dll
7FFE263E0000 GDI32.dll
7FFE25130000 gdi32full.dll
7FFE24A70000 msvcp_win.dll
7FFE25250000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE25550000 advapi32.dll
7FFE26D50000 msvcrt.dll
7FFE26220000 sechost.dll
7FFE25DE0000 RPCRT4.dll
7FFE249C0000 bcrypt.dll
7FFE241B0000 CRYPTBASE.DLL
7FFE24C70000 bcryptPrimitives.dll
7FFE26410000 IMM32.DLL
