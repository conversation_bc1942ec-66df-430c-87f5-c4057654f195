-- اسکریپت یکپارچه Supabase برای هماهنگی کامل با کدبیس
-- تاریخ: 2025-01-15
-- نسخه: 2.0

-- حذف جداول قدیمی
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- جدول کاربران با ID های UUID مطابق کدبیس
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    profile_image TEXT DEFAULT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول اعلانات
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    from_user_id UUID NOT NULL REFERENCES users(id),
    to_user_id UUID NOT NULL REFERENCES users(id),
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    payment_type VARCHAR(20) DEFAULT NULL,
    receiver VARCHAR(100) DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    transaction_data JSONB DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- جدول تراکنش‌های تایید شده
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    notification_id UUID REFERENCES notifications(id),
    user_id UUID NOT NULL REFERENCES users(id),
    partner_id UUID NOT NULL REFERENCES users(id),
    type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    payment_type VARCHAR(20) DEFAULT NULL,
    receiver VARCHAR(100) DEFAULT NULL,
    approved_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- درج کاربران با ID های صحیح مطابق کدبیس
INSERT INTO users (id, username, full_name, phone, password_hash) VALUES 
('ad28ba8f-0fa0-4420-8119-70fcacfd237e', 'Alikakai', 'علی کاکایی', '09172558813', 'Alikakai'),
('930b5d13-0408-4c57-965b-235c5532b35a', 'Miladnasiri', 'میلاد نصیری', '09184352395', 'Miladnasiri')
ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    password_hash = EXCLUDED.password_hash;

-- ایندکس‌ها برای بهبود عملکرد
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_from_user ON notifications(from_user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(transaction_type);
CREATE INDEX idx_notifications_created ON notifications(created_at DESC);
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_partner ON transactions(partner_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_created ON transactions(created_at DESC);
CREATE INDEX idx_users_username ON users(username);

-- تریگر برای بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- فعال‌سازی Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies صحیح با auth.uid()
-- Policies برای users
CREATE POLICY "Users can view all users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Policies برای notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid() = to_user_id OR auth.uid() = from_user_id);

CREATE POLICY "Users can insert notifications" ON notifications
    FOR INSERT WITH CHECK (auth.uid() = from_user_id);

CREATE POLICY "Users can update their received notifications" ON notifications
    FOR UPDATE USING (auth.uid() = to_user_id);

-- Policies برای transactions
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (auth.uid() = user_id OR auth.uid() = partner_id);

CREATE POLICY "Users can insert transactions when approving" ON transactions
    FOR INSERT WITH CHECK (auth.uid() = approved_by);

-- تابع برای تایید رمز عبور
CREATE OR REPLACE FUNCTION verify_user_credentials(username_input TEXT, password_input TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE username = username_input 
        AND password_hash = password_input 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای دریافت اطلاعات کاربر
CREATE OR REPLACE FUNCTION get_user_by_username(username_input TEXT)
RETURNS TABLE(
    id UUID,
    username TEXT,
    full_name TEXT,
    phone TEXT,
    email TEXT,
    profile_image TEXT,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.id, u.username, u.full_name, u.phone, u.email, u.profile_image, u.is_active
    FROM users u
    WHERE u.username = username_input AND u.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای بروزرسانی عکس پروفایل
CREATE OR REPLACE FUNCTION update_user_profile_image(user_id_input UUID, image_data TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE users 
    SET profile_image = image_data, updated_at = NOW()
    WHERE id = user_id_input;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای دریافت عکس پروفایل
CREATE OR REPLACE FUNCTION get_user_profile_image(username_input TEXT)
RETURNS TEXT AS $$
DECLARE
    image_data TEXT;
BEGIN
    SELECT profile_image INTO image_data
    FROM users
    WHERE username = username_input;
    
    RETURN image_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای درج تراکنش
CREATE OR REPLACE FUNCTION insert_transaction_notification(
    from_user_id_input UUID,
    to_user_id_input UUID,
    transaction_type_input TEXT,
    amount_input DECIMAL,
    product_count_input INTEGER,
    description_input TEXT,
    payment_type_input TEXT DEFAULT NULL,
    receiver_input TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notifications (
        from_user_id, to_user_id, transaction_type, amount, 
        product_count, description, payment_type, receiver
    ) VALUES (
        from_user_id_input, to_user_id_input, transaction_type_input, 
        amount_input, product_count_input, description_input,
        payment_type_input, receiver_input
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای تایید/رد اعلان
CREATE OR REPLACE FUNCTION approve_reject_notification(
    notification_id_input UUID,
    action_input TEXT, -- 'approved' or 'rejected'
    approver_id_input UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    notification_record RECORD;
    transaction_id UUID;
BEGIN
    -- دریافت اطلاعات اعلان
    SELECT * INTO notification_record
    FROM notifications
    WHERE id = notification_id_input AND to_user_id = approver_id_input;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- بروزرسانی وضعیت اعلان
    UPDATE notifications
    SET status = action_input, updated_at = NOW()
    WHERE id = notification_id_input;
    
    -- اگر تایید شد، تراکنش را در جدول transactions ثبت کن
    IF action_input = 'approved' THEN
        INSERT INTO transactions (
            notification_id, user_id, partner_id, type, amount,
            product_count, description, payment_type, receiver, approved_by
        ) VALUES (
            notification_record.id,
            notification_record.from_user_id,
            notification_record.to_user_id,
            notification_record.transaction_type,
            notification_record.amount,
            notification_record.product_count,
            notification_record.description,
            notification_record.payment_type,
            notification_record.receiver,
            approver_id_input
        ) RETURNING id INTO transaction_id;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای دریافت اعلانات فیلتر شده
CREATE OR REPLACE FUNCTION get_filtered_notifications(
    user_id_input UUID,
    notification_type TEXT DEFAULT NULL, -- 'received' or 'sent'
    status_filter TEXT DEFAULT NULL
)
RETURNS TABLE(
    id UUID,
    from_user_id UUID,
    to_user_id UUID,
    from_username TEXT,
    to_username TEXT,
    from_full_name TEXT,
    to_full_name TEXT,
    transaction_type TEXT,
    amount DECIMAL,
    product_count INTEGER,
    description TEXT,
    payment_type TEXT,
    receiver TEXT,
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.id, n.from_user_id, n.to_user_id,
        u1.username as from_username, u2.username as to_username,
        u1.full_name as from_full_name, u2.full_name as to_full_name,
        n.transaction_type, n.amount, n.product_count, n.description,
        n.payment_type, n.receiver, n.status,
        n.created_at, n.updated_at, n.read_at
    FROM notifications n
    JOIN users u1 ON n.from_user_id = u1.id
    JOIN users u2 ON n.to_user_id = u2.id
    WHERE 
        (notification_type IS NULL OR 
         (notification_type = 'received' AND n.to_user_id = user_id_input) OR
         (notification_type = 'sent' AND n.from_user_id = user_id_input))
        AND
        (status_filter IS NULL OR n.status = status_filter)
        AND
        (n.to_user_id = user_id_input OR n.from_user_id = user_id_input)
    ORDER BY n.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- تنظیمات Realtime
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE transactions;
ALTER PUBLICATION supabase_realtime ADD TABLE users;

-- Comments برای مستندسازی
COMMENT ON TABLE users IS 'جدول کاربران سیستم حسابداری شراکتی';
COMMENT ON TABLE notifications IS 'جدول اعلانات تراکنش‌ها';
COMMENT ON TABLE transactions IS 'جدول تراکنش‌های تایید شده';

-- تابع برای دریافت آمار مالی
CREATE OR REPLACE FUNCTION get_financial_summary(user_id_input UUID)
RETURNS TABLE(
    total_sales DECIMAL,
    total_expenses DECIMAL,
    total_withdrawals DECIMAL,
    net_profit DECIMAL,
    user_share DECIMAL,
    partner_balance DECIMAL
) AS $$
DECLARE
    partner_id UUID;
BEGIN
    -- پیدا کردن شریک
    SELECT CASE
        WHEN user_id_input = 'ad28ba8f-0fa0-4420-8119-70fcacfd237e' THEN '930b5d13-0408-4c57-965b-235c5532b35a'::UUID
        ELSE 'ad28ba8f-0fa0-4420-8119-70fcacfd237e'::UUID
    END INTO partner_id;

    RETURN QUERY
    WITH sales_data AS (
        SELECT COALESCE(SUM(amount), 0) as total_sales_amount
        FROM transactions
        WHERE type = 'sale' AND (user_id = user_id_input OR partner_id = user_id_input)
    ),
    expenses_data AS (
        SELECT COALESCE(SUM(amount), 0) as total_expenses_amount
        FROM transactions
        WHERE type = 'expense' AND (user_id = user_id_input OR partner_id = user_id_input)
    ),
    withdrawals_data AS (
        SELECT COALESCE(SUM(amount), 0) as total_withdrawals_amount
        FROM transactions
        WHERE type = 'withdrawal' AND user_id = user_id_input
    )
    SELECT
        s.total_sales_amount,
        e.total_expenses_amount,
        w.total_withdrawals_amount,
        (s.total_sales_amount - e.total_expenses_amount) as net_profit_amount,
        ((s.total_sales_amount - e.total_expenses_amount) / 2) - w.total_withdrawals_amount as user_share_amount,
        ((s.total_sales_amount - e.total_expenses_amount) / 2) - (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE type = 'withdrawal' AND user_id = partner_id
        ) as partner_balance_amount
    FROM sales_data s, expenses_data e, withdrawals_data w;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع برای بررسی وضعیت سیستم
CREATE OR REPLACE FUNCTION system_health_check()
RETURNS TABLE(
    table_name TEXT,
    record_count BIGINT,
    last_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 'users'::TEXT, COUNT(*)::BIGINT, MAX(updated_at)
    FROM users
    UNION ALL
    SELECT 'notifications'::TEXT, COUNT(*)::BIGINT, MAX(updated_at)
    FROM notifications
    UNION ALL
    SELECT 'transactions'::TEXT, COUNT(*)::BIGINT, MAX(created_at)
    FROM transactions;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
