-- تست سیستم احراز هویت ساده
-- برای 2 شریک بدون نیاز به تایید ایمیل/شماره

-- تست 1: بررسی وجود جداول
SELECT 'جداول موجود:' as test_name;
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'notifications', 'transactions')
ORDER BY table_name;

-- تست 2: بررسی کاربران
SELECT 'کاربران موجود:' as test_name;
SELECT id, username, full_name, phone, is_active, created_at 
FROM users 
ORDER BY username;

-- تست 3: تست ورود علی کاکایی
SELECT 'تست ورود علی کاکایی:' as test_name;
SELECT * FROM simple_login('Alikakai', 'Alika<PERSON>');

-- تست 4: تست ورود میلاد نصیری
SELECT 'تست ورود میلاد نصیری:' as test_name;
SELECT * FROM simple_login('Miladnasiri', 'Miladnasiri');

-- تست 5: تست ورود نامعتبر
SELECT 'تست ورود نامعتبر:' as test_name;
SELECT * FROM simple_login('invalid', 'invalid');

-- تست 6: دریافت اطلاعات شریک علی
SELECT 'شریک علی کاکایی:' as test_name;
SELECT * FROM get_partner_info('ad28ba8f-0fa0-4420-8119-70fcacfd237e');

-- تست 7: دریافت اطلاعات شریک میلاد
SELECT 'شریک میلاد نصیری:' as test_name;
SELECT * FROM get_partner_info('930b5d13-0408-4c57-965b-235c5532b35a');

-- تست 8: ثبت تراکنش نمونه
SELECT 'ثبت تراکنش نمونه:' as test_name;
SELECT register_simple_transaction(
    'ad28ba8f-0fa0-4420-8119-70fcacfd237e'::UUID,  -- علی
    'sale',
    150000.00,
    10,
    'فروش 10 بطری آب',
    'نقدی',
    'میلاد نصیری'
) as notification_id;

-- تست 9: بررسی اعلانات
SELECT 'اعلانات موجود:' as test_name;
SELECT 
    n.id,
    u1.full_name as from_user,
    u2.full_name as to_user,
    n.transaction_type,
    n.amount,
    n.product_count,
    n.description,
    n.status,
    n.created_at
FROM notifications n
JOIN users u1 ON n.from_user_id = u1.id
JOIN users u2 ON n.to_user_id = u2.id
ORDER BY n.created_at DESC;

-- تست 10: تایید اعلان (اگر اعلانی وجود دارد)
DO $$
DECLARE
    latest_notification_id UUID;
BEGIN
    -- پیدا کردن آخرین اعلان pending
    SELECT id INTO latest_notification_id
    FROM notifications 
    WHERE status = 'pending'
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF latest_notification_id IS NOT NULL THEN
        -- تایید اعلان
        PERFORM simple_approve_reject(
            latest_notification_id,
            'approved',
            '930b5d13-0408-4c57-965b-235c5532b35a'::UUID  -- میلاد تایید می‌کند
        );
        
        RAISE NOTICE 'اعلان % تایید شد', latest_notification_id;
    ELSE
        RAISE NOTICE 'هیچ اعلان pending پیدا نشد';
    END IF;
END $$;

-- تست 11: بررسی تراکنش‌های تایید شده
SELECT 'تراکنش‌های تایید شده:' as test_name;
SELECT 
    t.id,
    u1.full_name as user_name,
    u2.full_name as partner_name,
    t.type,
    t.amount,
    t.product_count,
    t.description,
    u3.full_name as approved_by,
    t.created_at
FROM transactions t
JOIN users u1 ON t.user_id = u1.id
JOIN users u2 ON t.partner_id = u2.id
JOIN users u3 ON t.approved_by = u3.id
ORDER BY t.created_at DESC;

-- تست 12: بررسی وضعیت سیستم
SELECT 'وضعیت کلی سیستم:' as test_name;
SELECT 
    'users' as table_name,
    COUNT(*) as record_count,
    MAX(updated_at) as last_activity
FROM users
UNION ALL
SELECT 
    'notifications' as table_name,
    COUNT(*) as record_count,
    MAX(updated_at) as last_activity
FROM notifications
UNION ALL
SELECT 
    'transactions' as table_name,
    COUNT(*) as record_count,
    MAX(created_at) as last_activity
FROM transactions;

-- تست 13: تست case insensitive ورود
SELECT 'تست ورود case insensitive:' as test_name;
SELECT 'alikakai lowercase:' as variant, (SELECT success FROM simple_login('alikakai', 'Alikakai'))
UNION ALL
SELECT 'ALIKAKAI uppercase:' as variant, (SELECT success FROM simple_login('ALIKAKAI', 'Alikakai'))
UNION ALL
SELECT 'miladnasiri lowercase:' as variant, (SELECT success FROM simple_login('miladnasiri', 'Miladnasiri'))
UNION ALL
SELECT 'MILADNASIRI uppercase:' as variant, (SELECT success FROM simple_login('MILADNASIRI', 'Miladnasiri'));

-- خلاصه نتایج
SELECT '=== خلاصه تست ===' as summary;
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM users) = 2 THEN '✅ کاربران: OK'
        ELSE '❌ کاربران: مشکل'
    END as users_status
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT success FROM simple_login('Alikakai', 'Alikakai')) THEN '✅ ورود علی: OK'
        ELSE '❌ ورود علی: مشکل'
    END as ali_login_status
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT success FROM simple_login('Miladnasiri', 'Miladnasiri')) THEN '✅ ورود میلاد: OK'
        ELSE '❌ ورود میلاد: مشکل'
    END as milad_login_status
UNION ALL
SELECT 
    CASE 
        WHEN NOT (SELECT success FROM simple_login('invalid', 'invalid')) THEN '✅ امنیت: OK'
        ELSE '❌ امنیت: مشکل'
    END as security_status;
