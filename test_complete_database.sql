-- ===================================================================
-- تست کامل دیتابیس حرفه‌ای سیستم حسابداری MA
-- ===================================================================

-- تست 1: بررسی ایجاد جداول
SELECT '=== تست 1: بررسی جداول ===' as test_section;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('users', 'notifications', 'transactions', 'transaction_categories', 
                           'payment_methods', 'financial_reports', 'system_settings', 
                           'activity_logs', 'user_sessions') THEN '✅'
        ELSE '❌'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- تست 2: بررسی کاربران
SELECT '=== تست 2: بررسی کاربران ===' as test_section;
SELECT 
    username, 
    full_name, 
    phone, 
    is_active,
    CASE WHEN is_active THEN '✅ فعال' ELSE '❌ غیرفعال' END as status
FROM users 
ORDER BY username;

-- تست 3: بررسی دسته‌بندی‌ها
SELECT '=== تست 3: دسته‌بندی تراکنش‌ها ===' as test_section;
SELECT 
    name, 
    name_fa, 
    CASE WHEN is_income THEN 'درآمد' ELSE 'هزینه' END as type,
    color,
    CASE WHEN is_active THEN '✅' ELSE '❌' END as status
FROM transaction_categories 
ORDER BY is_income DESC, name;

-- تست 4: بررسی روش‌های پرداخت
SELECT '=== تست 4: روش‌های پرداخت ===' as test_section;
SELECT 
    name, 
    name_fa, 
    icon,
    CASE WHEN is_active THEN '✅' ELSE '❌' END as status
FROM payment_methods 
ORDER BY name;

-- تست 5: بررسی تنظیمات سیستم
SELECT '=== تست 5: تنظیمات سیستم ===' as test_section;
SELECT 
    setting_key, 
    setting_value, 
    setting_type,
    CASE WHEN is_public THEN 'عمومی' ELSE 'خصوصی' END as access_level
FROM system_settings 
ORDER BY setting_key;

-- تست 6: تست احراز هویت علی کاکایی
SELECT '=== تست 6: احراز هویت علی ===' as test_section;
SELECT 
    success,
    username,
    full_name,
    CASE WHEN success THEN '✅ موفق' ELSE '❌ ناموفق' END as status,
    session_token IS NOT NULL as has_session
FROM authenticate_user('Alikakai', 'Alikakai');

-- تست 7: تست احراز هویت میلاد نصیری
SELECT '=== تست 7: احراز هویت میلاد ===' as test_section;
SELECT 
    success,
    username,
    full_name,
    CASE WHEN success THEN '✅ موفق' ELSE '❌ ناموفق' END as status,
    session_token IS NOT NULL as has_session
FROM authenticate_user('Miladnasiri', 'Miladnasiri');

-- تست 8: تست احراز هویت نامعتبر
SELECT '=== تست 8: احراز هویت نامعتبر ===' as test_section;
SELECT 
    success,
    CASE WHEN NOT success THEN '✅ امنیت OK' ELSE '❌ مشکل امنیتی' END as security_status
FROM authenticate_user('invalid', 'wrong');

-- تست 9: دریافت اطلاعات شریک
SELECT '=== تست 9: اطلاعات شریک ===' as test_section;
SELECT 
    partner_username,
    partner_full_name,
    partner_phone,
    '✅ یافت شد' as status
FROM get_partner_info('ad28ba8f-0fa0-4420-8119-70fcacfd237e'); -- علی می‌خواهد اطلاعات میلاد را ببیند

-- تست 10: ثبت تراکنش نمونه
SELECT '=== تست 10: ثبت تراکنش ===' as test_section;
SELECT 
    create_transaction_notification(
        'ad28ba8f-0fa0-4420-8119-70fcacfd237e'::UUID,  -- علی
        'sale',
        'sale',
        250000.00,
        15,
        'فروش 15 بطری آب معدنی',
        'cash',
        'مشتری محلی',
        CURRENT_DATE,
        'normal',
        NULL
    ) as notification_id,
    '✅ ثبت شد' as status;

-- تست 11: بررسی اعلانات
SELECT '=== تست 11: اعلانات موجود ===' as test_section;
SELECT 
    from_full_name,
    to_full_name,
    transaction_type,
    category_name_fa,
    amount,
    description,
    status,
    priority,
    CASE 
        WHEN status = 'pending' THEN '⏳ در انتظار'
        WHEN status = 'approved' THEN '✅ تایید شده'
        WHEN status = 'rejected' THEN '❌ رد شده'
    END as status_fa
FROM get_notifications('930b5d13-0408-4c57-965b-235c5532b35a', 'received', NULL, 10, 0); -- اعلانات دریافتی میلاد

-- تست 12: تایید اعلان
DO $$
DECLARE
    latest_notification_id UUID;
    approval_result BOOLEAN;
BEGIN
    -- پیدا کردن آخرین اعلان pending
    SELECT id INTO latest_notification_id
    FROM notifications 
    WHERE status = 'pending' AND to_user_id = '930b5d13-0408-4c57-965b-235c5532b35a'
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF latest_notification_id IS NOT NULL THEN
        -- تایید اعلان توسط میلاد
        SELECT approve_reject_notification(
            latest_notification_id,
            'approved',
            '930b5d13-0408-4c57-965b-235c5532b35a'::UUID
        ) INTO approval_result;
        
        RAISE NOTICE 'اعلان % توسط میلاد تایید شد: %', latest_notification_id, approval_result;
    ELSE
        RAISE NOTICE 'هیچ اعلان pending پیدا نشد';
    END IF;
END $$;

-- تست 13: بررسی تراکنش‌های تایید شده
SELECT '=== تست 13: تراکنش‌های تایید شده ===' as test_section;
SELECT 
    user_full_name,
    partner_full_name,
    approved_by_name,
    type,
    category_name_fa,
    amount,
    description,
    transaction_date,
    '✅ تایید شده' as status
FROM get_transactions('ad28ba8f-0fa0-4420-8119-70fcacfd237e', NULL, NULL, NULL, NULL, 10, 0); -- تراکنش‌های علی

-- تست 14: محاسبه آمار مالی
SELECT '=== تست 14: آمار مالی ===' as test_section;
SELECT 
    total_sales,
    total_expenses,
    gross_profit,
    net_profit,
    user_share,
    partner_share,
    user_balance,
    partner_balance,
    transaction_count,
    pending_notifications,
    CASE 
        WHEN user_balance >= 0 THEN '✅ موجودی مثبت'
        ELSE '⚠️ موجودی منفی'
    END as balance_status
FROM calculate_financial_summary('ad28ba8f-0fa0-4420-8119-70fcacfd237e', NULL, NULL); -- آمار علی

-- تست 15: بررسی Views
SELECT '=== تست 15: Views گزارش‌گیری ===' as test_section;

-- خلاصه تراکنش‌ها
SELECT 'خلاصه تراکنش‌ها:' as view_name, COUNT(*) as record_count
FROM transaction_summary
UNION ALL
-- آمار روزانه
SELECT 'آمار روزانه:' as view_name, COUNT(*) as record_count
FROM daily_stats
UNION ALL
-- اعلانات خوانده نشده
SELECT 'اعلانات خوانده نشده:' as view_name, COUNT(*) as record_count
FROM unread_notifications;

-- تست 16: بررسی توابع کمکی
SELECT '=== تست 16: توابع کمکی ===' as test_section;

-- تست تنظیمات عمومی
SELECT 'تنظیمات عمومی:' as function_name, COUNT(*) as result_count
FROM get_system_settings(true)
UNION ALL
-- تست تنظیمات کامل
SELECT 'تنظیمات کامل:' as function_name, COUNT(*) as result_count
FROM get_system_settings(false);

-- تست 17: بررسی ایندکس‌ها
SELECT '=== تست 17: ایندکس‌ها ===' as test_section;
SELECT 
    schemaname,
    tablename,
    indexname,
    '✅ موجود' as status
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- تست 18: بررسی RLS Policies
SELECT '=== تست 18: RLS Policies ===' as test_section;
SELECT 
    tablename,
    policyname,
    cmd,
    '✅ فعال' as status
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- تست 19: خلاصه وضعیت سیستم
SELECT '=== تست 19: خلاصه نهایی ===' as test_section;
SELECT 
    'کاربران' as component,
    (SELECT COUNT(*) FROM users WHERE is_active = true) as count,
    CASE WHEN (SELECT COUNT(*) FROM users WHERE is_active = true) = 2 THEN '✅' ELSE '❌' END as status
UNION ALL
SELECT 
    'دسته‌بندی‌ها' as component,
    (SELECT COUNT(*) FROM transaction_categories WHERE is_active = true) as count,
    CASE WHEN (SELECT COUNT(*) FROM transaction_categories WHERE is_active = true) >= 5 THEN '✅' ELSE '❌' END as status
UNION ALL
SELECT 
    'روش‌های پرداخت' as component,
    (SELECT COUNT(*) FROM payment_methods WHERE is_active = true) as count,
    CASE WHEN (SELECT COUNT(*) FROM payment_methods WHERE is_active = true) >= 3 THEN '✅' ELSE '❌' END as status
UNION ALL
SELECT 
    'تنظیمات' as component,
    (SELECT COUNT(*) FROM system_settings) as count,
    CASE WHEN (SELECT COUNT(*) FROM system_settings) >= 5 THEN '✅' ELSE '❌' END as status
UNION ALL
SELECT 
    'توابع' as component,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public' AND routine_type = 'FUNCTION') as count,
    CASE WHEN (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public' AND routine_type = 'FUNCTION') >= 10 THEN '✅' ELSE '❌' END as status;

-- پیام پایانی
SELECT '🎉 تست کامل دیتابیس با موفقیت انجام شد!' as final_message;
