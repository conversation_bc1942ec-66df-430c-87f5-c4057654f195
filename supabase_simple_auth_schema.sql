-- Sc<PERSON>a ساده احراز هویت برای 2 شریک
-- بدون نیاز به تایید ایمیل یا شماره
-- تاریخ: 2025-01-15

-- حذ<PERSON> جداول قدیمی
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- جدول کاربران ساده (فقط 2 شریک)
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image TEXT DEFAULT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدو<PERSON> اعلانات
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    from_user_id UUID NOT NULL REFERENCES users(id),
    to_user_id UUID NOT NULL REFERENCES users(id),
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    payment_type VARCHAR(20) DEFAULT NULL,
    receiver VARCHAR(100) DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- جدول تراکنش‌های تایید شده
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    notification_id UUID REFERENCES notifications(id),
    user_id UUID NOT NULL REFERENCES users(id),
    partner_id UUID NOT NULL REFERENCES users(id),
    type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    payment_type VARCHAR(20) DEFAULT NULL,
    receiver VARCHAR(100) DEFAULT NULL,
    approved_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- درج 2 شریک با اطلاعات کامل
INSERT INTO users (id, username, full_name, phone, password_hash) VALUES 
('ad28ba8f-0fa0-4420-8119-70fcacfd237e', 'Alikakai', 'علی کاکایی', '09172558813', 'Alikakai'),
('930b5d13-0408-4c57-965b-235c5532b35a', 'Miladnasiri', 'میلاد نصیری', '09184352395', 'Miladnasiri')
ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    password_hash = EXCLUDED.password_hash,
    is_active = true;

-- ایندکس‌های ضروری
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_from_user ON notifications(from_user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_partner ON transactions(partner_id);

-- تریگر برای بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- فعال‌سازی Row Level Security (اختیاری - چون فقط 2 کاربر داریم)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies ساده
CREATE POLICY "Allow all users to view all users" ON users FOR SELECT USING (true);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view all notifications" ON notifications FOR SELECT USING (true);
CREATE POLICY "Users can insert notifications" ON notifications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update notifications" ON notifications FOR UPDATE USING (true);

CREATE POLICY "Users can view all transactions" ON transactions FOR SELECT USING (true);
CREATE POLICY "Users can insert transactions" ON transactions FOR INSERT WITH CHECK (true);

-- تابع احراز هویت ساده
CREATE OR REPLACE FUNCTION simple_login(username_input TEXT, password_input TEXT)
RETURNS TABLE(
    success BOOLEAN,
    user_id UUID,
    username TEXT,
    full_name TEXT,
    phone TEXT,
    profile_image TEXT
) AS $$
DECLARE
    user_record RECORD;
BEGIN
    -- جستجوی کاربر
    SELECT * INTO user_record
    FROM users 
    WHERE username = username_input 
    AND password_hash = password_input 
    AND is_active = true;
    
    IF FOUND THEN
        -- بروزرسانی آخرین ورود
        UPDATE users 
        SET last_login = NOW() 
        WHERE id = user_record.id;
        
        -- برگرداندن اطلاعات موفق
        RETURN QUERY SELECT 
            true as success,
            user_record.id,
            user_record.username,
            user_record.full_name,
            user_record.phone,
            user_record.profile_image;
    ELSE
        -- ورود ناموفق
        RETURN QUERY SELECT 
            false as success,
            NULL::UUID as user_id,
            NULL::TEXT as username,
            NULL::TEXT as full_name,
            NULL::TEXT as phone,
            NULL::TEXT as profile_image;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع دریافت شریک
CREATE OR REPLACE FUNCTION get_partner_info(current_user_id UUID)
RETURNS TABLE(
    partner_id UUID,
    partner_username TEXT,
    partner_full_name TEXT,
    partner_phone TEXT,
    partner_profile_image TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.username,
        u.full_name,
        u.phone,
        u.profile_image
    FROM users u
    WHERE u.id != current_user_id 
    AND u.is_active = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع ثبت تراکنش ساده
CREATE OR REPLACE FUNCTION register_simple_transaction(
    from_user_id_input UUID,
    transaction_type_input TEXT,
    amount_input DECIMAL,
    product_count_input INTEGER DEFAULT NULL,
    description_input TEXT DEFAULT NULL,
    payment_type_input TEXT DEFAULT NULL,
    receiver_input TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    to_user_id_input UUID;
    notification_id UUID;
BEGIN
    -- پیدا کردن شریک
    SELECT id INTO to_user_id_input
    FROM users 
    WHERE id != from_user_id_input 
    AND is_active = true
    LIMIT 1;
    
    IF to_user_id_input IS NULL THEN
        RAISE EXCEPTION 'شریک پیدا نشد';
    END IF;
    
    -- درج اعلان
    INSERT INTO notifications (
        from_user_id, to_user_id, transaction_type, amount, 
        product_count, description, payment_type, receiver
    ) VALUES (
        from_user_id_input, to_user_id_input, transaction_type_input, 
        amount_input, product_count_input, description_input,
        payment_type_input, receiver_input
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع تایید/رد ساده
CREATE OR REPLACE FUNCTION simple_approve_reject(
    notification_id_input UUID,
    action_input TEXT, -- 'approved' or 'rejected'
    approver_id_input UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    notification_record RECORD;
BEGIN
    -- دریافت اطلاعات اعلان
    SELECT * INTO notification_record
    FROM notifications
    WHERE id = notification_id_input;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- بروزرسانی وضعیت
    UPDATE notifications
    SET status = action_input, updated_at = NOW()
    WHERE id = notification_id_input;
    
    -- اگر تایید شد، در جدول transactions ثبت کن
    IF action_input = 'approved' THEN
        INSERT INTO transactions (
            notification_id, user_id, partner_id, type, amount,
            product_count, description, payment_type, receiver, approved_by
        ) VALUES (
            notification_record.id,
            notification_record.from_user_id,
            notification_record.to_user_id,
            notification_record.transaction_type,
            notification_record.amount,
            notification_record.product_count,
            notification_record.description,
            notification_record.payment_type,
            notification_record.receiver,
            approver_id_input
        );
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Comments
COMMENT ON TABLE users IS 'جدول 2 شریک کسب‌وکار';
COMMENT ON TABLE notifications IS 'اعلانات تراکنش‌ها بین شرکا';
COMMENT ON TABLE transactions IS 'تراکنش‌های تایید شده';
COMMENT ON FUNCTION simple_login IS 'ورود ساده با نام کاربری و رمز عبور';
COMMENT ON FUNCTION get_partner_info IS 'دریافت اطلاعات شریک';
COMMENT ON FUNCTION register_simple_transaction IS 'ثبت تراکنش ساده';
COMMENT ON FUNCTION simple_approve_reject IS 'تایید/رد ساده اعلانات';
