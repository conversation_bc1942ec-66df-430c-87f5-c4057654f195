-- ===================================================================
-- دیتابیس کامل و حرفه‌ای سیستم حسابداری شراکتی MA
-- تاریخ: 2025-01-15
-- نسخه: 3.0 Professional
-- توسط: Augment Agent
-- ===================================================================

-- پاکسازی کامل دیتابیس
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO anon;
GRANT ALL ON SCHEMA public TO authenticated;

-- ===================================================================
-- 1. جداول اصلی سیستم
-- ===================================================================

-- جدول کاربران (شرکا)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    profile_image_url TEXT,
    profile_image_data TEXT, -- Base64 encoded image
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT users_username_length CHECK (length(username) >= 3),
    CONSTRAINT users_phone_format CHECK (phone ~ '^09[0-9]{9}$' OR phone IS NULL),
    CONSTRAINT users_email_format CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL)
);

-- جدول دسته‌بندی تراکنش‌ها
CREATE TABLE transaction_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    name_fa TEXT NOT NULL, -- نام فارسی
    description TEXT,
    icon TEXT, -- نام آیکون
    color TEXT DEFAULT '#000000', -- رنگ hex
    is_income BOOLEAN NOT NULL, -- آیا درآمد است یا هزینه
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول روش‌های پرداخت
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    name_fa TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول اعلانات
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- اطلاعات تراکنش
    transaction_type TEXT NOT NULL,
    category_id UUID REFERENCES transaction_categories(id),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    product_count INTEGER DEFAULT NULL CHECK (product_count >= 0),
    description TEXT,
    payment_method_id UUID REFERENCES payment_methods(id),
    receiver_name TEXT,
    
    -- وضعیت اعلان
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- زمان‌بندی
    transaction_date DATE DEFAULT CURRENT_DATE,
    due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    responded_at TIMESTAMP WITH TIME ZONE,
    
    -- متادیتا
    metadata JSONB DEFAULT '{}',
    attachments TEXT[], -- آرایه URL های فایل‌های ضمیمه
    
    -- Constraints
    CONSTRAINT notifications_from_to_different CHECK (from_user_id != to_user_id),
    CONSTRAINT notifications_due_date_valid CHECK (due_date >= transaction_date OR due_date IS NULL)
);

-- جدول تراکنش‌های تایید شده
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    
    -- اطراف تراکنش
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    partner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    approved_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- جزئیات تراکنش
    type TEXT NOT NULL,
    category_id UUID REFERENCES transaction_categories(id),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    product_count INTEGER DEFAULT NULL CHECK (product_count >= 0),
    description TEXT,
    payment_method_id UUID REFERENCES payment_methods(id),
    receiver_name TEXT,
    
    -- زمان‌بندی
    transaction_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- حسابداری
    is_reconciled BOOLEAN DEFAULT false, -- آیا تطبیق شده
    reconciled_at TIMESTAMP WITH TIME ZONE,
    reconciled_by UUID REFERENCES users(id),
    
    -- متادیتا
    metadata JSONB DEFAULT '{}',
    attachments TEXT[],
    
    -- Constraints
    CONSTRAINT transactions_user_partner_different CHECK (user_id != partner_id)
);

-- جدول گزارش‌های مالی
CREATE TABLE financial_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type TEXT NOT NULL CHECK (report_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    generated_by UUID NOT NULL REFERENCES users(id),
    
    -- آمار کلی
    total_sales DECIMAL(15,2) DEFAULT 0,
    total_expenses DECIMAL(15,2) DEFAULT 0,
    total_withdrawals DECIMAL(15,2) DEFAULT 0,
    net_profit DECIMAL(15,2) DEFAULT 0,
    
    -- سهم شرکا
    user1_share DECIMAL(15,2) DEFAULT 0,
    user2_share DECIMAL(15,2) DEFAULT 0,
    user1_balance DECIMAL(15,2) DEFAULT 0,
    user2_balance DECIMAL(15,2) DEFAULT 0,
    
    -- جزئیات گزارش
    report_data JSONB NOT NULL DEFAULT '{}',
    summary TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT financial_reports_date_range CHECK (end_date >= start_date)
);

-- جدول تنظیمات سیستم
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- آیا برای کاربران عادی قابل دسترس است
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول لاگ فعالیت‌ها
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL, -- 'transaction', 'notification', 'user', etc.
    entity_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول جلسات کاربری
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    device_info TEXT,
    ip_address INET,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 2. ایندکس‌های بهینه‌سازی
-- ===================================================================

-- ایندکس‌های کاربران
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active) WHERE is_active = true;
CREATE INDEX idx_users_last_login ON users(last_login DESC);

-- ایندکس‌های اعلانات
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_from_user ON notifications(from_user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_created ON notifications(created_at DESC);
CREATE INDEX idx_notifications_unread ON notifications(to_user_id, read_at) WHERE read_at IS NULL;
CREATE INDEX idx_notifications_pending ON notifications(status, created_at) WHERE status = 'pending';

-- ایندکس‌های تراکنش‌ها
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_partner ON transactions(partner_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date DESC);
CREATE INDEX idx_transactions_amount ON transactions(amount DESC);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_category ON transactions(category_id);
CREATE INDEX idx_transactions_unreconciled ON transactions(is_reconciled) WHERE is_reconciled = false;

-- ایندکس‌های گزارش‌ها
CREATE INDEX idx_financial_reports_date_range ON financial_reports(start_date, end_date);
CREATE INDEX idx_financial_reports_type ON financial_reports(report_type);
CREATE INDEX idx_financial_reports_generated_by ON financial_reports(generated_by);

-- ایندکس‌های لاگ
CREATE INDEX idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_entity ON activity_logs(entity_type, entity_id);
CREATE INDEX idx_activity_logs_created ON activity_logs(created_at DESC);

-- ایندکس‌های جلسات
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active, expires_at) WHERE is_active = true;

-- ===================================================================
-- 3. تریگرها و توابع کمکی
-- ===================================================================

-- تابع بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تریگرهای بروزرسانی
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تابع لاگ فعالیت‌ها
CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
DECLARE
    current_user_id UUID;
    action_type TEXT;
BEGIN
    -- تشخیص نوع عملیات
    IF TG_OP = 'INSERT' THEN
        action_type := 'CREATE';
    ELSIF TG_OP = 'UPDATE' THEN
        action_type := 'UPDATE';
    ELSIF TG_OP = 'DELETE' THEN
        action_type := 'DELETE';
    END IF;

    -- دریافت ID کاربر فعلی (از session یا context)
    current_user_id := current_setting('app.current_user_id', true)::UUID;

    -- ثبت لاگ
    INSERT INTO activity_logs (
        user_id, action, entity_type, entity_id,
        old_values, new_values
    ) VALUES (
        current_user_id,
        action_type,
        TG_TABLE_NAME,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.id
            ELSE NEW.id
        END,
        CASE
            WHEN TG_OP = 'DELETE' THEN row_to_json(OLD)
            WHEN TG_OP = 'UPDATE' THEN row_to_json(OLD)
            ELSE NULL
        END,
        CASE
            WHEN TG_OP = 'INSERT' THEN row_to_json(NEW)
            WHEN TG_OP = 'UPDATE' THEN row_to_json(NEW)
            ELSE NULL
        END
    );

    RETURN CASE
        WHEN TG_OP = 'DELETE' THEN OLD
        ELSE NEW
    END;
END;
$$ LANGUAGE plpgsql;

-- تریگرهای لاگ
CREATE TRIGGER log_transactions_activity
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW EXECUTE FUNCTION log_activity();

CREATE TRIGGER log_notifications_activity
    AFTER INSERT OR UPDATE OR DELETE ON notifications
    FOR EACH ROW EXECUTE FUNCTION log_activity();

-- تابع پاکسازی جلسات منقضی
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions
    WHERE expires_at < NOW() OR is_active = false;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================================================================
-- 4. داده‌های اولیه
-- ===================================================================

-- درج کاربران اصلی
INSERT INTO users (id, username, full_name, phone, password_hash) VALUES
('ad28ba8f-0fa0-4420-8119-70fcacfd237e', 'Alikakai', 'علی کاکایی', '09172558813', 'Alikakai'),
('930b5d13-0408-4c57-965b-235c5532b35a', 'Miladnasiri', 'میلاد نصیری', '09184352395', 'Miladnasiri')
ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    password_hash = EXCLUDED.password_hash,
    is_active = true;

-- درج دسته‌بندی‌های تراکنش
INSERT INTO transaction_categories (name, name_fa, description, icon, color, is_income) VALUES
('sale', 'فروش', 'فروش محصولات', 'shopping-cart', '#4CAF50', true),
('purchase', 'خرید', 'خرید مواد اولیه', 'shopping-bag', '#FF9800', false),
('expense', 'هزینه', 'هزینه‌های عمومی', 'receipt', '#F44336', false),
('withdrawal', 'برداشت', 'برداشت سرمایه', 'credit-card', '#9C27B0', false),
('deposit', 'واریز', 'واریز سرمایه', 'account-balance', '#2196F3', true),
('salary', 'حقوق', 'پرداخت حقوق', 'person', '#607D8B', false),
('rent', 'اجاره', 'اجاره مغازه/انبار', 'home', '#795548', false),
('utility', 'خدمات', 'آب، برق، گاز', 'build', '#FF5722', false),
('transport', 'حمل‌ونقل', 'هزینه حمل‌ونقل', 'local-shipping', '#3F51B5', false),
('marketing', 'بازاریابی', 'تبلیغات و بازاریابی', 'campaign', '#E91E63', false)
ON CONFLICT (name) DO UPDATE SET
    name_fa = EXCLUDED.name_fa,
    description = EXCLUDED.description,
    icon = EXCLUDED.icon,
    color = EXCLUDED.color,
    is_income = EXCLUDED.is_income;

-- درج روش‌های پرداخت
INSERT INTO payment_methods (name, name_fa, description, icon) VALUES
('cash', 'نقدی', 'پرداخت نقدی', 'payments'),
('card', 'کارت', 'پرداخت با کارت', 'credit-card'),
('transfer', 'انتقال', 'انتقال بانکی', 'account-balance'),
('check', 'چک', 'پرداخت با چک', 'receipt-long'),
('installment', 'قسطی', 'پرداخت قسطی', 'schedule'),
('credit', 'اعتباری', 'فروش اعتباری', 'account-balance-wallet')
ON CONFLICT (name) DO UPDATE SET
    name_fa = EXCLUDED.name_fa,
    description = EXCLUDED.description,
    icon = EXCLUDED.icon;

-- تنظیمات اولیه سیستم
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_name', 'سیستم حسابداری MA', 'string', 'نام اپلیکیشن', true),
('app_version', '3.0.0', 'string', 'نسخه اپلیکیشن', true),
('currency', 'تومان', 'string', 'واحد پول', true),
('profit_share_ratio', '50:50', 'string', 'نسبت تقسیم سود', false),
('auto_approve_threshold', '0', 'number', 'حد آستانه تایید خودکار', false),
('notification_retention_days', '90', 'number', 'مدت نگهداری اعلانات (روز)', false),
('session_timeout_hours', '24', 'number', 'مدت انقضای جلسه (ساعت)', false),
('backup_enabled', 'true', 'boolean', 'فعال‌سازی پشتیبان‌گیری', false),
('theme', 'auto', 'string', 'تم اپلیکیشن (light/dark/auto)', true),
('language', 'fa', 'string', 'زبان پیش‌فرض', true)
ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    description = EXCLUDED.description,
    is_public = EXCLUDED.is_public;

-- ===================================================================
-- 5. توابع کسب‌وکار (Business Logic Functions)
-- ===================================================================

-- تابع احراز هویت ساده
CREATE OR REPLACE FUNCTION authenticate_user(username_input TEXT, password_input TEXT)
RETURNS TABLE(
    success BOOLEAN,
    user_id UUID,
    username TEXT,
    full_name TEXT,
    phone TEXT,
    email TEXT,
    profile_image_url TEXT,
    session_token TEXT
) AS $$
DECLARE
    found_user RECORD;
    new_session_token TEXT;
BEGIN
    -- جستجوی کاربر
    SELECT u.* INTO found_user
    FROM users u
    WHERE u.username = username_input
    AND u.password_hash = password_input
    AND u.is_active = true;

    IF found_user.id IS NOT NULL THEN
        -- ایجاد session token
        new_session_token := encode(gen_random_bytes(32), 'hex');

        -- ثبت جلسه جدید
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (found_user.id, new_session_token, NOW() + INTERVAL '24 hours');

        -- بروزرسانی آخرین ورود
        UPDATE users
        SET last_login = NOW(), login_count = login_count + 1
        WHERE id = found_user.id;

        -- برگرداندن اطلاعات موفق
        RETURN QUERY SELECT
            true,
            found_user.id,
            found_user.username,
            found_user.full_name,
            found_user.phone,
            found_user.email,
            found_user.profile_image_url,
            new_session_token;
    ELSE
        -- ورود ناموفق
        RETURN QUERY SELECT
            false,
            NULL::UUID,
            NULL::TEXT,
            NULL::TEXT,
            NULL::TEXT,
            NULL::TEXT,
            NULL::TEXT,
            NULL::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع خروج از سیستم
CREATE OR REPLACE FUNCTION logout_user(session_token_input TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_sessions
    SET is_active = false
    WHERE session_token = session_token_input;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع دریافت اطلاعات شریک
CREATE OR REPLACE FUNCTION get_partner_info(current_user_id UUID)
RETURNS TABLE(
    partner_id UUID,
    partner_username TEXT,
    partner_full_name TEXT,
    partner_phone TEXT,
    partner_email TEXT,
    partner_profile_image_url TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.username,
        u.full_name,
        u.phone,
        u.email,
        u.profile_image_url
    FROM users u
    WHERE u.id != current_user_id
    AND u.is_active = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع ثبت تراکنش جدید
CREATE OR REPLACE FUNCTION create_transaction_notification(
    from_user_id_input UUID,
    transaction_type_input TEXT,
    category_name_input TEXT,
    amount_input DECIMAL,
    product_count_input INTEGER DEFAULT NULL,
    description_input TEXT DEFAULT NULL,
    payment_method_name_input TEXT DEFAULT 'cash',
    receiver_name_input TEXT DEFAULT NULL,
    transaction_date_input DATE DEFAULT CURRENT_DATE,
    priority_input TEXT DEFAULT 'normal',
    attachments_input TEXT[] DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    to_user_id_input UUID;
    category_id_var UUID;
    payment_method_id_var UUID;
    notification_id UUID;
BEGIN
    -- پیدا کردن شریک
    SELECT id INTO to_user_id_input
    FROM users
    WHERE id != from_user_id_input
    AND is_active = true
    LIMIT 1;

    IF to_user_id_input IS NULL THEN
        RAISE EXCEPTION 'شریک پیدا نشد';
    END IF;

    -- پیدا کردن دسته‌بندی
    SELECT id INTO category_id_var
    FROM transaction_categories
    WHERE name = category_name_input AND is_active = true;

    -- پیدا کردن روش پرداخت
    SELECT id INTO payment_method_id_var
    FROM payment_methods
    WHERE name = payment_method_name_input AND is_active = true;

    -- درج اعلان
    INSERT INTO notifications (
        from_user_id, to_user_id, transaction_type, category_id,
        amount, product_count, description, payment_method_id,
        receiver_name, transaction_date, priority, attachments
    ) VALUES (
        from_user_id_input, to_user_id_input, transaction_type_input, category_id_var,
        amount_input, product_count_input, description_input, payment_method_id_var,
        receiver_name_input, transaction_date_input, priority_input, attachments_input
    ) RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع تایید/رد اعلان
CREATE OR REPLACE FUNCTION approve_reject_notification(
    notification_id_input UUID,
    action_input TEXT, -- 'approved' or 'rejected'
    approver_id_input UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    notification_record RECORD;
    transaction_id UUID;
BEGIN
    -- دریافت اطلاعات اعلان
    SELECT * INTO notification_record
    FROM notifications
    WHERE id = notification_id_input
    AND to_user_id = approver_id_input
    AND status = 'pending';

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- بروزرسانی وضعیت اعلان
    UPDATE notifications
    SET
        status = action_input,
        responded_at = NOW(),
        updated_at = NOW()
    WHERE id = notification_id_input;

    -- اگر تایید شد، تراکنش را در جدول transactions ثبت کن
    IF action_input = 'approved' THEN
        INSERT INTO transactions (
            notification_id, user_id, partner_id, approved_by,
            type, category_id, amount, product_count, description,
            payment_method_id, receiver_name, transaction_date, attachments
        ) VALUES (
            notification_record.id,
            notification_record.from_user_id,
            notification_record.to_user_id,
            approver_id_input,
            notification_record.transaction_type,
            notification_record.category_id,
            notification_record.amount,
            notification_record.product_count,
            notification_record.description,
            notification_record.payment_method_id,
            notification_record.receiver_name,
            notification_record.transaction_date,
            notification_record.attachments
        ) RETURNING id INTO transaction_id;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع دریافت اعلانات فیلتر شده
CREATE OR REPLACE FUNCTION get_notifications(
    user_id_input UUID,
    notification_type TEXT DEFAULT NULL, -- 'received', 'sent', 'all'
    status_filter TEXT DEFAULT NULL,
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    from_user_id UUID,
    to_user_id UUID,
    from_username TEXT,
    to_username TEXT,
    from_full_name TEXT,
    to_full_name TEXT,
    transaction_type TEXT,
    category_name TEXT,
    category_name_fa TEXT,
    amount DECIMAL,
    product_count INTEGER,
    description TEXT,
    payment_method_name TEXT,
    payment_method_name_fa TEXT,
    receiver_name TEXT,
    status TEXT,
    priority TEXT,
    transaction_date DATE,
    created_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    is_read BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        n.id, n.from_user_id, n.to_user_id,
        u1.username as from_username, u2.username as to_username,
        u1.full_name as from_full_name, u2.full_name as to_full_name,
        n.transaction_type,
        tc.name as category_name, tc.name_fa as category_name_fa,
        n.amount, n.product_count, n.description,
        pm.name as payment_method_name, pm.name_fa as payment_method_name_fa,
        n.receiver_name, n.status, n.priority, n.transaction_date,
        n.created_at, n.read_at,
        (n.read_at IS NOT NULL) as is_read
    FROM notifications n
    JOIN users u1 ON n.from_user_id = u1.id
    JOIN users u2 ON n.to_user_id = u2.id
    LEFT JOIN transaction_categories tc ON n.category_id = tc.id
    LEFT JOIN payment_methods pm ON n.payment_method_id = pm.id
    WHERE
        (notification_type IS NULL OR notification_type = 'all' OR
         (notification_type = 'received' AND n.to_user_id = user_id_input) OR
         (notification_type = 'sent' AND n.from_user_id = user_id_input))
        AND
        (status_filter IS NULL OR n.status = status_filter)
        AND
        (n.to_user_id = user_id_input OR n.from_user_id = user_id_input)
    ORDER BY n.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع علامت‌گذاری اعلان به عنوان خوانده شده
CREATE OR REPLACE FUNCTION mark_notification_as_read(
    notification_id_input UUID,
    user_id_input UUID
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notifications
    SET read_at = NOW()
    WHERE id = notification_id_input
    AND to_user_id = user_id_input
    AND read_at IS NULL;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع دریافت تراکنش‌ها
CREATE OR REPLACE FUNCTION get_transactions(
    user_id_input UUID,
    transaction_type_filter TEXT DEFAULT NULL,
    category_filter TEXT DEFAULT NULL,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL,
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    partner_id UUID,
    user_full_name TEXT,
    partner_full_name TEXT,
    approved_by_name TEXT,
    type TEXT,
    category_name TEXT,
    category_name_fa TEXT,
    amount DECIMAL,
    product_count INTEGER,
    description TEXT,
    payment_method_name TEXT,
    payment_method_name_fa TEXT,
    receiver_name TEXT,
    transaction_date DATE,
    created_at TIMESTAMP WITH TIME ZONE,
    is_reconciled BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.id, t.user_id, t.partner_id,
        u1.full_name as user_full_name,
        u2.full_name as partner_full_name,
        u3.full_name as approved_by_name,
        t.type,
        tc.name as category_name, tc.name_fa as category_name_fa,
        t.amount, t.product_count, t.description,
        pm.name as payment_method_name, pm.name_fa as payment_method_name_fa,
        t.receiver_name, t.transaction_date, t.created_at, t.is_reconciled
    FROM transactions t
    JOIN users u1 ON t.user_id = u1.id
    JOIN users u2 ON t.partner_id = u2.id
    JOIN users u3 ON t.approved_by = u3.id
    LEFT JOIN transaction_categories tc ON t.category_id = tc.id
    LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
    WHERE
        (t.user_id = user_id_input OR t.partner_id = user_id_input)
        AND
        (transaction_type_filter IS NULL OR t.type = transaction_type_filter)
        AND
        (category_filter IS NULL OR tc.name = category_filter)
        AND
        (start_date IS NULL OR t.transaction_date >= start_date)
        AND
        (end_date IS NULL OR t.transaction_date <= end_date)
    ORDER BY t.transaction_date DESC, t.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع محاسبه آمار مالی
CREATE OR REPLACE FUNCTION calculate_financial_summary(
    user_id_input UUID,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL
)
RETURNS TABLE(
    total_sales DECIMAL,
    total_expenses DECIMAL,
    total_withdrawals DECIMAL,
    user_withdrawals DECIMAL,
    partner_withdrawals DECIMAL,
    gross_profit DECIMAL,
    net_profit DECIMAL,
    user_share DECIMAL,
    partner_share DECIMAL,
    user_balance DECIMAL,
    partner_balance DECIMAL,
    transaction_count INTEGER,
    pending_notifications INTEGER
) AS $$
DECLARE
    partner_id UUID;
    sales_amount DECIMAL := 0;
    expenses_amount DECIMAL := 0;
    user_withdrawals_amount DECIMAL := 0;
    partner_withdrawals_amount DECIMAL := 0;
    total_withdrawals_amount DECIMAL := 0;
    gross_profit_amount DECIMAL := 0;
    net_profit_amount DECIMAL := 0;
    equal_share DECIMAL := 0;
    user_balance_amount DECIMAL := 0;
    partner_balance_amount DECIMAL := 0;
    trans_count INTEGER := 0;
    pending_count INTEGER := 0;
BEGIN
    -- پیدا کردن شریک
    SELECT id INTO partner_id
    FROM users
    WHERE id != user_id_input AND is_active = true
    LIMIT 1;

    -- محاسبه فروش‌ها
    SELECT COALESCE(SUM(amount), 0) INTO sales_amount
    FROM transactions t
    JOIN transaction_categories tc ON t.category_id = tc.id
    WHERE (t.user_id = user_id_input OR t.partner_id = user_id_input)
    AND tc.is_income = true
    AND (start_date IS NULL OR t.transaction_date >= start_date)
    AND (end_date IS NULL OR t.transaction_date <= end_date);

    -- محاسبه هزینه‌ها
    SELECT COALESCE(SUM(amount), 0) INTO expenses_amount
    FROM transactions t
    JOIN transaction_categories tc ON t.category_id = tc.id
    WHERE (t.user_id = user_id_input OR t.partner_id = user_id_input)
    AND tc.is_income = false
    AND t.type != 'withdrawal'
    AND (start_date IS NULL OR t.transaction_date >= start_date)
    AND (end_date IS NULL OR t.transaction_date <= end_date);

    -- محاسبه برداشت کاربر
    SELECT COALESCE(SUM(amount), 0) INTO user_withdrawals_amount
    FROM transactions t
    WHERE t.user_id = user_id_input
    AND t.type = 'withdrawal'
    AND (start_date IS NULL OR t.transaction_date >= start_date)
    AND (end_date IS NULL OR t.transaction_date <= end_date);

    -- محاسبه برداشت شریک
    SELECT COALESCE(SUM(amount), 0) INTO partner_withdrawals_amount
    FROM transactions t
    WHERE t.user_id = partner_id
    AND t.type = 'withdrawal'
    AND (start_date IS NULL OR t.transaction_date >= start_date)
    AND (end_date IS NULL OR t.transaction_date <= end_date);

    -- محاسبه تعداد تراکنش‌ها
    SELECT COUNT(*) INTO trans_count
    FROM transactions t
    WHERE (t.user_id = user_id_input OR t.partner_id = user_id_input)
    AND (start_date IS NULL OR t.transaction_date >= start_date)
    AND (end_date IS NULL OR t.transaction_date <= end_date);

    -- محاسبه اعلانات در انتظار
    SELECT COUNT(*) INTO pending_count
    FROM notifications n
    WHERE n.to_user_id = user_id_input
    AND n.status = 'pending';

    -- محاسبات نهایی
    total_withdrawals_amount := user_withdrawals_amount + partner_withdrawals_amount;
    gross_profit_amount := sales_amount - expenses_amount;
    net_profit_amount := gross_profit_amount - total_withdrawals_amount;
    equal_share := gross_profit_amount / 2;
    user_balance_amount := equal_share - user_withdrawals_amount;
    partner_balance_amount := equal_share - partner_withdrawals_amount;

    RETURN QUERY SELECT
        sales_amount,
        expenses_amount,
        total_withdrawals_amount,
        user_withdrawals_amount,
        partner_withdrawals_amount,
        gross_profit_amount,
        net_profit_amount,
        equal_share,
        equal_share,
        user_balance_amount,
        partner_balance_amount,
        trans_count,
        pending_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع بروزرسانی عکس پروفایل
CREATE OR REPLACE FUNCTION update_profile_image(
    user_id_input UUID,
    image_url_input TEXT DEFAULT NULL,
    image_data_input TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE users
    SET
        profile_image_url = image_url_input,
        profile_image_data = image_data_input,
        updated_at = NOW()
    WHERE id = user_id_input;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع دریافت تنظیمات سیستم
CREATE OR REPLACE FUNCTION get_system_settings(is_public_only BOOLEAN DEFAULT true)
RETURNS TABLE(
    setting_key TEXT,
    setting_value TEXT,
    setting_type TEXT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.setting_key,
        s.setting_value,
        s.setting_type,
        s.description
    FROM system_settings s
    WHERE (NOT is_public_only OR s.is_public = true)
    ORDER BY s.setting_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تابع بروزرسانی تنظیمات
CREATE OR REPLACE FUNCTION update_system_setting(
    setting_key_input TEXT,
    setting_value_input TEXT,
    updated_by_input UUID
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE system_settings
    SET
        setting_value = setting_value_input,
        updated_by = updated_by_input,
        updated_at = NOW()
    WHERE setting_key = setting_key_input;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================================================================
-- 6. Row Level Security (RLS) Policies
-- ===================================================================

-- فعال‌سازی RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- Policies برای users
CREATE POLICY "Users can view all users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Policies برای notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid() = to_user_id OR auth.uid() = from_user_id);

CREATE POLICY "Users can insert notifications" ON notifications
    FOR INSERT WITH CHECK (auth.uid() = from_user_id);

CREATE POLICY "Users can update their received notifications" ON notifications
    FOR UPDATE USING (auth.uid() = to_user_id);

-- Policies برای transactions
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (auth.uid() = user_id OR auth.uid() = partner_id);

CREATE POLICY "Users can insert transactions when approving" ON transactions
    FOR INSERT WITH CHECK (auth.uid() = approved_by);

-- Policies برای financial_reports
CREATE POLICY "Users can view their own reports" ON financial_reports
    FOR SELECT USING (auth.uid() = generated_by);

CREATE POLICY "Users can create reports" ON financial_reports
    FOR INSERT WITH CHECK (auth.uid() = generated_by);

-- Policies برای activity_logs
CREATE POLICY "Users can view their own activity logs" ON activity_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Policies برای user_sessions
CREATE POLICY "Users can view their own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- ===================================================================
-- 7. Views برای گزارش‌گیری
-- ===================================================================

-- View خلاصه تراکنش‌ها
CREATE OR REPLACE VIEW transaction_summary AS
SELECT
    t.id,
    t.transaction_date,
    u1.full_name as user_name,
    u2.full_name as partner_name,
    tc.name_fa as category_name,
    pm.name_fa as payment_method,
    t.type,
    t.amount,
    t.description,
    t.is_reconciled,
    t.created_at
FROM transactions t
JOIN users u1 ON t.user_id = u1.id
JOIN users u2 ON t.partner_id = u2.id
LEFT JOIN transaction_categories tc ON t.category_id = tc.id
LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
ORDER BY t.transaction_date DESC, t.created_at DESC;

-- View آمار روزانه
CREATE OR REPLACE VIEW daily_stats AS
SELECT
    t.transaction_date,
    COUNT(*) as transaction_count,
    SUM(CASE WHEN tc.is_income THEN t.amount ELSE 0 END) as daily_income,
    SUM(CASE WHEN NOT tc.is_income THEN t.amount ELSE 0 END) as daily_expense,
    SUM(CASE WHEN tc.is_income THEN t.amount ELSE -t.amount END) as daily_net
FROM transactions t
LEFT JOIN transaction_categories tc ON t.category_id = tc.id
GROUP BY t.transaction_date
ORDER BY t.transaction_date DESC;

-- View اعلانات خوانده نشده
CREATE OR REPLACE VIEW unread_notifications AS
SELECT
    n.id,
    n.to_user_id,
    u1.full_name as from_user_name,
    n.transaction_type,
    tc.name_fa as category_name,
    n.amount,
    n.description,
    n.priority,
    n.created_at,
    EXTRACT(EPOCH FROM (NOW() - n.created_at))/3600 as hours_ago
FROM notifications n
JOIN users u1 ON n.from_user_id = u1.id
LEFT JOIN transaction_categories tc ON n.category_id = tc.id
WHERE n.read_at IS NULL AND n.status = 'pending'
ORDER BY n.priority DESC, n.created_at DESC;

-- ===================================================================
-- 8. Permissions و Grant ها
-- ===================================================================

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;

-- Grant برای Views
GRANT SELECT ON transaction_summary TO anon, authenticated;
GRANT SELECT ON daily_stats TO anon, authenticated;
GRANT SELECT ON unread_notifications TO anon, authenticated;

-- ===================================================================
-- 9. تنظیمات Realtime
-- ===================================================================

-- فعال‌سازی Realtime برای جداول مهم
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE transactions;
ALTER PUBLICATION supabase_realtime ADD TABLE users;
ALTER PUBLICATION supabase_realtime ADD TABLE user_sessions;

-- ===================================================================
-- 10. Comments و مستندسازی
-- ===================================================================

-- Comments برای جداول
COMMENT ON TABLE users IS 'جدول کاربران سیستم (شرکای کسب‌وکار)';
COMMENT ON TABLE transaction_categories IS 'دسته‌بندی انواع تراکنش‌ها';
COMMENT ON TABLE payment_methods IS 'روش‌های پرداخت';
COMMENT ON TABLE notifications IS 'اعلانات تراکنش‌ها بین شرکا';
COMMENT ON TABLE transactions IS 'تراکنش‌های تایید شده';
COMMENT ON TABLE financial_reports IS 'گزارش‌های مالی تولید شده';
COMMENT ON TABLE system_settings IS 'تنظیمات سیستم';
COMMENT ON TABLE activity_logs IS 'لاگ فعالیت‌های کاربران';
COMMENT ON TABLE user_sessions IS 'جلسات کاربری فعال';

-- Comments برای توابع
COMMENT ON FUNCTION authenticate_user IS 'احراز هویت کاربر و ایجاد session';
COMMENT ON FUNCTION logout_user IS 'خروج از سیستم و غیرفعال کردن session';
COMMENT ON FUNCTION get_partner_info IS 'دریافت اطلاعات شریک کسب‌وکار';
COMMENT ON FUNCTION create_transaction_notification IS 'ایجاد اعلان تراکنش جدید';
COMMENT ON FUNCTION approve_reject_notification IS 'تایید یا رد اعلان تراکنش';
COMMENT ON FUNCTION get_notifications IS 'دریافت اعلانات با فیلتر';
COMMENT ON FUNCTION get_transactions IS 'دریافت تراکنش‌ها با فیلتر';
COMMENT ON FUNCTION calculate_financial_summary IS 'محاسبه خلاصه آمار مالی';
COMMENT ON FUNCTION update_profile_image IS 'بروزرسانی عکس پروفایل کاربر';

-- ===================================================================
-- پایان فایل - دیتابیس آماده استفاده است!
-- ===================================================================

SELECT 'دیتابیس حرفه‌ای سیستم حسابداری MA با موفقیت ایجاد شد! 🎉' as status;
