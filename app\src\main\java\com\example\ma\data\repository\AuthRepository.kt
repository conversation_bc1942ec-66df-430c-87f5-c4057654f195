package com.example.ma.data.repository

import android.content.Context
import android.content.SharedPreferences
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.example.ma.data.model.User
import com.example.ma.data.remote.SupabaseClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * Repository برای مدیریت احراز هویت
 */
class AuthRepository(private val context: Context) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)

    // اتصال به Supabase برای آینده

    companion object {
        private const val KEY_CURRENT_USER_ID = "current_user_id"
        private const val KEY_CURRENT_USERNAME = "current_username"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
    }
    
    /**
     * ورود ساده کاربر (فقط 2 شریک - بدون تایید ایمیل/شماره)
     */
    suspend fun login(username: String, password: String): User? = withContext(Dispatchers.IO) {
        try {
            println("🔐 AuthRepository.login() - ورود ساده شروع شد")
            println("📝 Username: '$username'")

            // اعتبارسنجی ورودی
            if (username.isBlank() || password.isBlank()) {
                println("❌ Username یا Password خالی است")
                return@withContext null
            }

            // بررسی اتصال اینترنت
            val networkAvailable = isNetworkAvailable()
            println("🌐 Network Available: $networkAvailable")

            if (!networkAvailable) {
                println("📱 Fallback به ورود آفلاین")
                return@withContext loginOffline(username, password)
            }

            // احراز هویت با Supabase
            val authResult = SupabaseClient.authenticateUser(username, password)

            when (authResult) {
                is com.example.ma.data.model.ApiResult.Success -> {
                    if (authResult.data) {
                        println("✅ احراز هویت موفق")

                        // دریافت اطلاعات کاربر از SharedPreferences (که در SupabaseClient ذخیره شده)
                        val userId = sharedPreferences.getString("current_user_id", "") ?: ""
                        val fullName = sharedPreferences.getString("current_full_name", "") ?: ""
                        val phone = sharedPreferences.getString("current_phone", "") ?: ""

                        // ایجاد User object
                        val user = User(
                            id = userId,
                            username = username,
                            displayName = fullName,
                            phone = phone,
                            isActive = true
                        )

                        // ذخیره وضعیت ورود
                        saveLoginState(user)

                        println("✅ کاربر وارد شد: ${user.displayName}")
                        user
                    } else {
                        println("❌ احراز هویت ناموفق")
                        null
                    }
                }
                is com.example.ma.data.model.ApiResult.Error -> {
                    println("❌ خطا در احراز هویت: ${authResult.error.message}")
                    // Fallback به ورود آفلاین
                    loginOffline(username, password)
                // Fallback به داده‌های محلی
                return@withContext loginOffline(username, password)
            }

            // بررسی رمز عبور در Supabase
            println("🔍 بررسی رمز عبور در Supabase...")
            val passwordValid = verifyPasswordInSupabase(username, password)
            println("✅ Password Valid: $passwordValid")

            if (passwordValid) {
                println("👤 دریافت اطلاعات کاربر از Supabase...")
                // دریافت اطلاعات کاربر از Supabase
                val userInfo = getUserFromSupabase(username)
                println("📋 User Info: $userInfo")

                userInfo?.let { user: User ->
                    println("💾 ذخیره اطلاعات ورود...")
                    // ذخیره اطلاعات ورود
                    sharedPreferences.edit()
                        .putString(KEY_CURRENT_USER_ID, user.id)
                        .putString(KEY_CURRENT_USERNAME, user.username)
                        .putBoolean(KEY_IS_LOGGED_IN, true)
                        .apply()

                    // ذخیره اطلاعات کاربر در ProfileManager
                    saveUserProfileData(user)

                    println("🎉 ورود موفق: ${user.displayName}")
                    return@withContext user
                }
            } else {
                println("📱 Password نامعتبر، Fallback به ورود آفلاین")
                return@withContext loginOffline(username, password)
            }

            println("❌ ورود ناموفق")
            return@withContext null

        } catch (e: Exception) {
            println("💥 خطا در ورود: ${e.message}")
            e.printStackTrace()
            // در صورت خطا، fallback به داده‌های محلی
            return@withContext loginOffline(username, password)
        }
    }
    
    /**
     * خروج کاربر
     */
    fun logout() {
        println("🚪 AuthRepository.logout() شروع شد")
        println("📝 قبل از logout - isLoggedIn: ${isLoggedIn()}")
        println("📝 قبل از logout - currentUserId: ${sharedPreferences.getString(KEY_CURRENT_USER_ID, "null")}")

        sharedPreferences.edit()
            .remove(KEY_CURRENT_USER_ID)
            .putBoolean(KEY_IS_LOGGED_IN, false)
            .clear() // پاک کردن همه SharedPreferences
            .putBoolean(KEY_IS_LOGGED_IN, false) // دوباره تنظیم
            .apply()

        println("✅ بعد از logout - isLoggedIn: ${isLoggedIn()}")
        println("✅ بعد از logout - currentUserId: ${sharedPreferences.getString(KEY_CURRENT_USER_ID, "null")}")

        // پاک کردن کامل session
        clearSession()
    }

    /**
     * پاک کردن کامل session
     */
    private fun clearSession() {
        println("🧹 clearSession() شروع شد")
        try {
            // پاک کردن همه SharedPreferences
            sharedPreferences.edit().clear().apply()

            // تنظیم مجدد وضعیت خروج
            sharedPreferences.edit()
                .putBoolean(KEY_IS_LOGGED_IN, false)
                .apply()

            println("✅ Session کاملاً پاک شد")
        } catch (e: Exception) {
            println("❌ خطا در پاک کردن session: ${e.message}")
        }
    }
    
    /**
     * بررسی وضعیت ورود
     */
    fun isLoggedIn(): Boolean {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
        val currentUserId = sharedPreferences.getString(KEY_CURRENT_USER_ID, "null")
        println("🔍 isLoggedIn() چک شد: $isLoggedIn, currentUserId: $currentUserId")
        return isLoggedIn
    }
    
    /**
     * دریافت کاربر فعلی (بدون suspend - برای UI)
     */
    fun getCurrentUserSync(): User? {
        val userId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return userId?.let { id: String ->
            User.getAllUsers().find { it.id == id }?.copy(isActive = true)
        }
    }

    /**
     * دریافت کاربر فعلی (ساده)
     */
    suspend fun getCurrentUser(): User? = withContext(Dispatchers.IO) {
        val userId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return@withContext userId?.let { id: String ->
            User.getAllUsers().find { it.id == id }?.copy(isActive = true)
        }
    }

    /**
     * دریافت کاربر مقابل
     */
    suspend fun getOtherUser(): User? = withContext(Dispatchers.IO) {
        val currentUserId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return@withContext User.getAllUsers().find { it.id != currentUserId }
    }

    /**
     * بررسی رمز عبور در Supabase
     */
    private suspend fun verifyPasswordInSupabase(username: String, password: String): Boolean {
        return try {
            val response = SupabaseClient.callFunction("verify_password", mapOf(
                "username_input" to username,
                "password_input" to password
            ))
            response == "true"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * دریافت اطلاعات کاربر از Supabase
     */
    private suspend fun getUserFromSupabase(username: String): User? {
        return try {
            val response = SupabaseClient.getUser(username)
            response?.let { userMap ->
                User(
                    id = userMap["id"] as? String ?: "",
                    username = userMap["username"] as? String ?: username,
                    displayName = userMap["full_name"] as? String ?: username,
                    email = userMap["email"] as? String,
                    phone = userMap["phone"] as? String,
                    isActive = userMap["is_active"] as? Boolean ?: true
                )
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * ورود آفلاین ساده (فقط 2 شریک)
     */
    private fun loginOffline(username: String, password: String): User? {
        println("📱 loginOffline() - ورود آفلاین ساده")
        println("📝 Username: '$username'")

        // بررسی ساده 2 شریک
        val user = when {
            (username.equals("Miladnasiri", ignoreCase = true) ||
             username.equals("miladnasiri", ignoreCase = true) ||
             username.equals("milad", ignoreCase = true)) && password == "Miladnasiri" -> {
                println("✅ میلاد نصیری شناسایی شد")
                User(
                    id = "930b5d13-0408-4c57-965b-235c5532b35a",
                    username = "Miladnasiri",
                    displayName = "میلاد نصیری",
                    phone = "09184352395",
                    isActive = true
                )
            }
            (username.equals("Alikakai", ignoreCase = true) ||
             username.equals("alikakai", ignoreCase = true) ||
             username.equals("ali", ignoreCase = true)) && password == "Alikakai" -> {
                println("✅ علی کاکایی شناسایی شد")
                User(
                    id = "ad28ba8f-0fa0-4420-8119-70fcacfd237e",
                    username = "Alikakai",
                    displayName = "علی کاکایی",
                    phone = "09172558813",
                    isActive = true
                )
            }
            else -> {
                println("❌ کاربر شناسایی نشد")
                println("📝 ورودی: username='$username', password='$password'")
                println("📝 انتظار: 'miladnasiri'/'Miladnasiri' یا 'alikakai'/'Alikakai'")
                null
            }
        }

        user?.let { u: User ->
            println("💾 ذخیره اطلاعات ورود آفلاین...")
            saveLoginState(u)
            println("✅ ورود آفلاین موفق: ${u.displayName}")
        } ?: println("❌ ورود آفلاین ناموفق")

        return user
    }

    /**
     * ذخیره وضعیت ورود کاربر
     */
    private fun saveLoginState(user: User) {
        sharedPreferences.edit().apply {
            putString(KEY_CURRENT_USER_ID, user.id)
            putString(KEY_CURRENT_USERNAME, user.username)
            putString("current_full_name", user.displayName)
            putString("current_phone", user.phone ?: "")
            putBoolean(KEY_IS_LOGGED_IN, true)
            apply()
        }
        println("💾 وضعیت ورود ذخیره شد: ${user.displayName}")
    }

    /**
     * دریافت اطلاعات کاربر از دیتابیس
     */
    suspend fun getUserInfo(username: String): User? {
        return withContext(Dispatchers.IO) {
            try {
                getUserFromSupabase(username)
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * ذخیره اطلاعات کاربر در پروفایل
     */
    private fun saveUserProfileData(user: User) {
        val profilePrefs = context.getSharedPreferences("user_profile", Context.MODE_PRIVATE)
        profilePrefs.edit().apply {
            putString("full_name", user.displayName)
            putString("email", user.email ?: "")
            putString("phone", user.phone ?: "")
            putBoolean("profile_completed", true)
            // اگر کاربر عکس پروفایل داره، ذخیره کن
            user.profileImage?.let { profileImage ->
                putString("profile_image", profileImage)
            }
            apply()
        }

        // بازیابی عکس پروفایل از Supabase در background
        try {
            // بازیابی عکس پروفایل از Supabase
            GlobalScope.launch {
                try {
                    val profileImage = SupabaseClient.getUserProfileImage(user.username)
                    if (!profileImage.isNullOrEmpty()) {
                        // ذخیره عکس بازیابی شده
                        profilePrefs.edit()
                            .putString("profile_image", profileImage)
                            .apply()
                        println("✅ عکس پروفایل از Supabase بازیابی شد برای ${user.username}")
                    } else {
                        println("❌ عکس پروفایل در Supabase پیدا نشد برای ${user.username}")
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    println("❌ خطا در بازیابی عکس پروفایل از Supabase: ${e.message}")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            println("خطا در شروع بازیابی عکس پروفایل: ${e.message}")
        }
    }

    /**
     * بررسی اتصال اینترنت
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                return networkInfo?.isConnected == true
            }
        } catch (e: Exception) {
            false
        }
    }
}
